# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "mevita: 1 messages, 0 services")

set(MSG_I_FLAGS "-Imevita:/home/<USER>/mevita_ws/src/mevita/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(mevita_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_custom_target(_mevita_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "mevita" "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" "std_msgs/Header"
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(mevita
  "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/mevita
)

### Generating Services

### Generating Module File
_generate_module_cpp(mevita
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/mevita
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(mevita_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(mevita_generate_messages mevita_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_dependencies(mevita_generate_messages_cpp _mevita_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(mevita_gencpp)
add_dependencies(mevita_gencpp mevita_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS mevita_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(mevita
  "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/mevita
)

### Generating Services

### Generating Module File
_generate_module_eus(mevita
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/mevita
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(mevita_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(mevita_generate_messages mevita_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_dependencies(mevita_generate_messages_eus _mevita_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(mevita_geneus)
add_dependencies(mevita_geneus mevita_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS mevita_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(mevita
  "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/mevita
)

### Generating Services

### Generating Module File
_generate_module_lisp(mevita
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/mevita
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(mevita_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(mevita_generate_messages mevita_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_dependencies(mevita_generate_messages_lisp _mevita_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(mevita_genlisp)
add_dependencies(mevita_genlisp mevita_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS mevita_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(mevita
  "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/mevita
)

### Generating Services

### Generating Module File
_generate_module_nodejs(mevita
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/mevita
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(mevita_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(mevita_generate_messages mevita_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_dependencies(mevita_generate_messages_nodejs _mevita_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(mevita_gennodejs)
add_dependencies(mevita_gennodejs mevita_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS mevita_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(mevita
  "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/std_msgs/cmake/../msg/Header.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/mevita
)

### Generating Services

### Generating Module File
_generate_module_py(mevita
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/mevita
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(mevita_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(mevita_generate_messages mevita_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg" NAME_WE)
add_dependencies(mevita_generate_messages_py _mevita_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(mevita_genpy)
add_dependencies(mevita_genpy mevita_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS mevita_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/mevita)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/mevita
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(mevita_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/mevita)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/mevita
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(mevita_generate_messages_eus std_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/mevita)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/mevita
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(mevita_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/mevita)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/mevita
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(mevita_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/mevita)
  install(CODE "execute_process(COMMAND \"/home/<USER>/miniconda3/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/mevita\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/mevita
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(mevita_generate_messages_py std_msgs_generate_messages_py)
endif()
