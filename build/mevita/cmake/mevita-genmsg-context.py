# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/mevita_ws/src/mevita/msg/MevitaLog.msg"
services_str = ""
pkg_name = "mevita"
dependencies_str = "std_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "mevita;/home/<USER>/mevita_ws/src/mevita/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/home/<USER>/miniconda3/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
