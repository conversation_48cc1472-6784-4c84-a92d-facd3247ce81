set(_CATKIN_CURRENT_PACKAGE "mevita")
set(mevita_VERSION "1.0.0")
set(mevita_MAINTAINER " <ka<PERSON><PERSON><PERSON>@jsk.imi.i.u-tokyo.ac.jp>")
set(mevita_PACKAGE_FORMAT "2")
set(mevita_BUILD_DEPENDS "roslaunch" "robot_state_publisher" "rviz" "joint_state_publisher_gui" "gazebo")
set(mevita_BUILD_EXPORT_DEPENDS "roslaunch" "robot_state_publisher" "rviz" "joint_state_publisher_gui" "gazebo")
set(mevita_BUILDTOOL_DEPENDS "catkin")
set(mevita_BUILDTOOL_EXPORT_DEPENDS )
set(mevita_EXEC_DEPENDS "roslaunch" "robot_state_publisher" "rviz" "joint_state_publisher_gui" "gazebo")
set(mevita_RUN_DEPENDS "roslaunch" "robot_state_publisher" "rviz" "joint_state_publisher_gui" "gazebo")
set(mevita_TEST_DEPENDS )
set(mevita_DOC_DEPENDS )
set(mevita_URL_WEBSITE "")
set(mevita_URL_BUGTRACKER "")
set(mevita_URL_REPOSITORY "")
set(mevita_DEPRECATED "")