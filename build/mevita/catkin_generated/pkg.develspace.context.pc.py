# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/mevita_ws/devel/.private/mevita/include".split(';') if "/home/<USER>/mevita_ws/devel/.private/mevita/include" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "mevita"
PROJECT_SPACE_DIR = "/home/<USER>/mevita_ws/devel/.private/mevita"
PROJECT_VERSION = "1.0.0"
