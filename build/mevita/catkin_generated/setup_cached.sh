#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/mevita_ws/devel/.private/mevita:$CMAKE_PREFIX_PATH"
export PWD='/home/<USER>/mevita_ws/build/mevita'
export ROSLISP_PACKAGE_DIRECTORIES="/home/<USER>/mevita_ws/devel/.private/mevita/share/common-lisp:$ROSLISP_PACKAGE_DIRECTORIES"
export ROS_PACKAGE_PATH="/home/<USER>/mevita_ws/src/mevita:$ROS_PACKAGE_PATH"