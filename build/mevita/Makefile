# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/mevita_ws/src/mevita

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/mevita_ws/build/mevita

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/local/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/local/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mevita_ws/build/mevita/CMakeFiles /home/<USER>/mevita_ws/build/mevita//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/mevita_ws/build/mevita/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named download_extra_data

# Build rule for target.
download_extra_data: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 download_extra_data
.PHONY : download_extra_data

# fast build rule for target.
download_extra_data/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/download_extra_data.dir/build.make CMakeFiles/download_extra_data.dir/build
.PHONY : download_extra_data/fast

#=============================================================================
# Target rules for targets named tests

# Build rule for target.
tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests
.PHONY : tests

# fast build rule for target.
tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/build
.PHONY : tests/fast

#=============================================================================
# Target rules for targets named run_tests

# Build rule for target.
run_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 run_tests
.PHONY : run_tests

# fast build rule for target.
run_tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
.PHONY : run_tests/fast

#=============================================================================
# Target rules for targets named clean_test_results

# Build rule for target.
clean_test_results: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean_test_results
.PHONY : clean_test_results

# fast build rule for target.
clean_test_results/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean_test_results.dir/build.make CMakeFiles/clean_test_results.dir/build
.PHONY : clean_test_results/fast

#=============================================================================
# Target rules for targets named doxygen

# Build rule for target.
doxygen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 doxygen
.PHONY : doxygen

# fast build rule for target.
doxygen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/doxygen.dir/build.make CMakeFiles/doxygen.dir/build
.PHONY : doxygen/fast

#=============================================================================
# Target rules for targets named _catkin_empty_exported_target

# Build rule for target.
_catkin_empty_exported_target: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _catkin_empty_exported_target
.PHONY : _catkin_empty_exported_target

# fast build rule for target.
_catkin_empty_exported_target/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_catkin_empty_exported_target.dir/build.make CMakeFiles/_catkin_empty_exported_target.dir/build
.PHONY : _catkin_empty_exported_target/fast

#=============================================================================
# Target rules for targets named std_msgs_generate_messages_cpp

# Build rule for target.
std_msgs_generate_messages_cpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 std_msgs_generate_messages_cpp
.PHONY : std_msgs_generate_messages_cpp

# fast build rule for target.
std_msgs_generate_messages_cpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/std_msgs_generate_messages_cpp.dir/build.make CMakeFiles/std_msgs_generate_messages_cpp.dir/build
.PHONY : std_msgs_generate_messages_cpp/fast

#=============================================================================
# Target rules for targets named std_msgs_generate_messages_eus

# Build rule for target.
std_msgs_generate_messages_eus: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 std_msgs_generate_messages_eus
.PHONY : std_msgs_generate_messages_eus

# fast build rule for target.
std_msgs_generate_messages_eus/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/std_msgs_generate_messages_eus.dir/build.make CMakeFiles/std_msgs_generate_messages_eus.dir/build
.PHONY : std_msgs_generate_messages_eus/fast

#=============================================================================
# Target rules for targets named std_msgs_generate_messages_lisp

# Build rule for target.
std_msgs_generate_messages_lisp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 std_msgs_generate_messages_lisp
.PHONY : std_msgs_generate_messages_lisp

# fast build rule for target.
std_msgs_generate_messages_lisp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/std_msgs_generate_messages_lisp.dir/build.make CMakeFiles/std_msgs_generate_messages_lisp.dir/build
.PHONY : std_msgs_generate_messages_lisp/fast

#=============================================================================
# Target rules for targets named std_msgs_generate_messages_nodejs

# Build rule for target.
std_msgs_generate_messages_nodejs: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 std_msgs_generate_messages_nodejs
.PHONY : std_msgs_generate_messages_nodejs

# fast build rule for target.
std_msgs_generate_messages_nodejs/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/std_msgs_generate_messages_nodejs.dir/build.make CMakeFiles/std_msgs_generate_messages_nodejs.dir/build
.PHONY : std_msgs_generate_messages_nodejs/fast

#=============================================================================
# Target rules for targets named std_msgs_generate_messages_py

# Build rule for target.
std_msgs_generate_messages_py: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 std_msgs_generate_messages_py
.PHONY : std_msgs_generate_messages_py

# fast build rule for target.
std_msgs_generate_messages_py/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/std_msgs_generate_messages_py.dir/build.make CMakeFiles/std_msgs_generate_messages_py.dir/build
.PHONY : std_msgs_generate_messages_py/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages

# Build rule for target.
mevita_generate_messages: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages
.PHONY : mevita_generate_messages

# fast build rule for target.
mevita_generate_messages/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages.dir/build.make CMakeFiles/mevita_generate_messages.dir/build
.PHONY : mevita_generate_messages/fast

#=============================================================================
# Target rules for targets named _mevita_generate_messages_check_deps_MevitaLog

# Build rule for target.
_mevita_generate_messages_check_deps_MevitaLog: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _mevita_generate_messages_check_deps_MevitaLog
.PHONY : _mevita_generate_messages_check_deps_MevitaLog

# fast build rule for target.
_mevita_generate_messages_check_deps_MevitaLog/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_mevita_generate_messages_check_deps_MevitaLog.dir/build.make CMakeFiles/_mevita_generate_messages_check_deps_MevitaLog.dir/build
.PHONY : _mevita_generate_messages_check_deps_MevitaLog/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages_cpp

# Build rule for target.
mevita_generate_messages_cpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages_cpp
.PHONY : mevita_generate_messages_cpp

# fast build rule for target.
mevita_generate_messages_cpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages_cpp.dir/build.make CMakeFiles/mevita_generate_messages_cpp.dir/build
.PHONY : mevita_generate_messages_cpp/fast

#=============================================================================
# Target rules for targets named mevita_gencpp

# Build rule for target.
mevita_gencpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_gencpp
.PHONY : mevita_gencpp

# fast build rule for target.
mevita_gencpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_gencpp.dir/build.make CMakeFiles/mevita_gencpp.dir/build
.PHONY : mevita_gencpp/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages_eus

# Build rule for target.
mevita_generate_messages_eus: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages_eus
.PHONY : mevita_generate_messages_eus

# fast build rule for target.
mevita_generate_messages_eus/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages_eus.dir/build.make CMakeFiles/mevita_generate_messages_eus.dir/build
.PHONY : mevita_generate_messages_eus/fast

#=============================================================================
# Target rules for targets named mevita_geneus

# Build rule for target.
mevita_geneus: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_geneus
.PHONY : mevita_geneus

# fast build rule for target.
mevita_geneus/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_geneus.dir/build.make CMakeFiles/mevita_geneus.dir/build
.PHONY : mevita_geneus/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages_lisp

# Build rule for target.
mevita_generate_messages_lisp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages_lisp
.PHONY : mevita_generate_messages_lisp

# fast build rule for target.
mevita_generate_messages_lisp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages_lisp.dir/build.make CMakeFiles/mevita_generate_messages_lisp.dir/build
.PHONY : mevita_generate_messages_lisp/fast

#=============================================================================
# Target rules for targets named mevita_genlisp

# Build rule for target.
mevita_genlisp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_genlisp
.PHONY : mevita_genlisp

# fast build rule for target.
mevita_genlisp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_genlisp.dir/build.make CMakeFiles/mevita_genlisp.dir/build
.PHONY : mevita_genlisp/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages_nodejs

# Build rule for target.
mevita_generate_messages_nodejs: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages_nodejs
.PHONY : mevita_generate_messages_nodejs

# fast build rule for target.
mevita_generate_messages_nodejs/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages_nodejs.dir/build.make CMakeFiles/mevita_generate_messages_nodejs.dir/build
.PHONY : mevita_generate_messages_nodejs/fast

#=============================================================================
# Target rules for targets named mevita_gennodejs

# Build rule for target.
mevita_gennodejs: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_gennodejs
.PHONY : mevita_gennodejs

# fast build rule for target.
mevita_gennodejs/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_gennodejs.dir/build.make CMakeFiles/mevita_gennodejs.dir/build
.PHONY : mevita_gennodejs/fast

#=============================================================================
# Target rules for targets named mevita_generate_messages_py

# Build rule for target.
mevita_generate_messages_py: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_generate_messages_py
.PHONY : mevita_generate_messages_py

# fast build rule for target.
mevita_generate_messages_py/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_generate_messages_py.dir/build.make CMakeFiles/mevita_generate_messages_py.dir/build
.PHONY : mevita_generate_messages_py/fast

#=============================================================================
# Target rules for targets named mevita_genpy

# Build rule for target.
mevita_genpy: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mevita_genpy
.PHONY : mevita_genpy

# fast build rule for target.
mevita_genpy/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mevita_genpy.dir/build.make CMakeFiles/mevita_genpy.dir/build
.PHONY : mevita_genpy/fast

#=============================================================================
# Target rules for targets named gmock

# Build rule for target.
gmock: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock
.PHONY : gmock

# fast build rule for target.
gmock/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googlemock/CMakeFiles/gmock.dir/build.make gtest/googlemock/CMakeFiles/gmock.dir/build
.PHONY : gmock/fast

#=============================================================================
# Target rules for targets named gmock_main

# Build rule for target.
gmock_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock_main
.PHONY : gmock_main

# fast build rule for target.
gmock_main/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googlemock/CMakeFiles/gmock_main.dir/build.make gtest/googlemock/CMakeFiles/gmock_main.dir/build
.PHONY : gmock_main/fast

#=============================================================================
# Target rules for targets named gtest

# Build rule for target.
gtest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gtest
.PHONY : gtest

# fast build rule for target.
gtest/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googletest/CMakeFiles/gtest.dir/build.make gtest/googletest/CMakeFiles/gtest.dir/build
.PHONY : gtest/fast

#=============================================================================
# Target rules for targets named gtest_main

# Build rule for target.
gtest_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gtest_main
.PHONY : gtest_main

# fast build rule for target.
gtest_main/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googletest/CMakeFiles/gtest_main.dir/build.make gtest/googletest/CMakeFiles/gtest_main.dir/build
.PHONY : gtest_main/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... _catkin_empty_exported_target"
	@echo "... _mevita_generate_messages_check_deps_MevitaLog"
	@echo "... clean_test_results"
	@echo "... download_extra_data"
	@echo "... doxygen"
	@echo "... mevita_gencpp"
	@echo "... mevita_generate_messages"
	@echo "... mevita_generate_messages_cpp"
	@echo "... mevita_generate_messages_eus"
	@echo "... mevita_generate_messages_lisp"
	@echo "... mevita_generate_messages_nodejs"
	@echo "... mevita_generate_messages_py"
	@echo "... mevita_geneus"
	@echo "... mevita_genlisp"
	@echo "... mevita_gennodejs"
	@echo "... mevita_genpy"
	@echo "... run_tests"
	@echo "... std_msgs_generate_messages_cpp"
	@echo "... std_msgs_generate_messages_eus"
	@echo "... std_msgs_generate_messages_lisp"
	@echo "... std_msgs_generate_messages_nodejs"
	@echo "... std_msgs_generate_messages_py"
	@echo "... tests"
	@echo "... gmock"
	@echo "... gmock_main"
	@echo "... gtest"
	@echo "... gtest_main"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

