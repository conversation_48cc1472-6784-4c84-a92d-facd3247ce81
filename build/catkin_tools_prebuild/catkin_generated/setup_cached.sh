#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/mevita_ws/devel/.private/catkin_tools_prebuild:$CMAKE_PREFIX_PATH"
export PATH='/opt/ros/noetic/bin:/home/<USER>/Music/pbrs/node-v22.17.1-linux-x64/bin:/home/<USER>/node-v20.13.1-linux-x64/bin:/home/<USER>/Music/pbrs/node-v22.17.1-linux-x64/bin:/home/<USER>/node-v20.13.1-linux-x64/bin:/home/<USER>/miniconda3/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'
export PWD='/home/<USER>/mevita_ws/build/catkin_tools_prebuild'
export ROSLISP_PACKAGE_DIRECTORIES='/home/<USER>/mevita_ws/devel/.private/catkin_tools_prebuild/share/common-lisp'
export ROS_PACKAGE_PATH="/home/<USER>/mevita_ws/build/catkin_tools_prebuild:$ROS_PACKAGE_PATH"