#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Xbox手柄测试脚本
用于验证Xbox手柄的按键映射和功能
"""

import rospy
from sensor_msgs.msg import Joy

class XboxControllerTester:
    def __init__(self):
        rospy.init_node('xbox_controller_tester')
        
        # 订阅Xbox手柄话题
        rospy.Subscriber("/xboxjoy/joy", Joy, self.xbox_callback, queue_size=1)
        
        print("Xbox手柄测试器已启动")
        print("请按Xbox手柄上的按键进行测试...")
        print("按键映射:")
        print("  A按键 (索引0): 站立/坐下切换")
        print("  B按键 (索引1): 站立/行走切换")
        print("  左摇杆X轴 (索引0): 左右移动")
        print("  左摇杆Y轴 (索引1): 前后移动")
        print("  右摇杆X轴 (索引3): 转向控制")
        print("按Ctrl+C退出测试")
        print("-" * 50)
        
    def xbox_callback(self, msg):
        """Xbox手柄回调函数"""
        axes = msg.axes
        buttons = msg.buttons
        
        # 检查按键状态
        if len(buttons) > 0 and buttons[0] == 1:
            print("✓ A按键被按下 - 触发站立/坐下切换")
            
        if len(buttons) > 1 and buttons[1] == 1:
            print("✓ B按键被按下 - 触发站立/行走切换")
            
        # 检查摇杆状态（只在有明显移动时显示）
        if len(axes) >= 4:
            left_x = axes[0]
            left_y = axes[1]
            right_x = axes[3] if len(axes) > 3 else 0.0
            
            # 设置死区阈值
            threshold = 0.1
            
            if abs(left_x) > threshold or abs(left_y) > threshold or abs(right_x) > threshold:
                print(f"摇杆状态 - 左摇杆: X={left_x:.2f}, Y={left_y:.2f} | 右摇杆X: {right_x:.2f}")
                
                # 显示控制命令
                commands = [left_y, left_x, right_x, right_x]
                print(f"控制命令: [前后={commands[0]:.2f}, 左右={commands[1]:.2f}, 转向={commands[2]:.2f}]")
        
        # 显示完整的原始数据（可选，用于调试）
        # print(f"原始数据 - 轴: {axes}, 按键: {buttons}")

def main():
    try:
        tester = XboxControllerTester()
        rospy.spin()
    except rospy.ROSInterruptException:
        print("\n测试结束")
    except KeyboardInterrupt:
        print("\n测试被用户中断")

if __name__ == '__main__':
    main()
