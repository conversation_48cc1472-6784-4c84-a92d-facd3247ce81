# Xbox手柄控制指南

本指南介绍如何使用Xbox手柄控制MEVITA机器人，完全替代PS4手柄。

## 🎮 Xbox手柄按键映射

| 功能 | Xbox手柄按键 | PS4手柄对应 | 说明 |
|------|-------------|------------|------|
| 站立/坐下切换 | A按键 | Cross (×) | 底部绿色按键 |
| 站立/行走切换 | B按键 | Circle (○) | 右侧红色按键 |
| 前后移动 | 左摇杆Y轴 | 左摇杆Y轴 | 向上推进，向下退 |
| 左右移动 | 左摇杆X轴 | 左摇杆X轴 | 向左推左移，向右推右移 |
| 转向控制 | 右摇杆X轴 | 右摇杆X轴 | 向左推左转，向右推右转 |

## 🚀 快速开始

### 1. 硬件连接
- 将Xbox手柄通过USB连接到计算机
- 或使用蓝牙连接Xbox手柄
- 确保手柄被识别为 `/dev/input/js0` 或 `/dev/input/js1`

### 2. 启动系统（仿真模式）
```bash
# 终端1: 启动ROS核心
roscore

# 终端2: 启动Xbox手柄节点
roslaunch mevita xbox_controller.launch

# 终端3: 启动仿真
cd /path/to/mevita_ws/src/mevita
python3 scripts/mevita_main.py --sim
```

### 3. 启动系统（真实机器人）
```bash
# 终端1: 启动ROS核心
roscore

# 终端2: 启动Xbox手柄和传感器
roslaunch mevita xbox_controller.launch

# 终端3: 设置CAN总线并启动控制
./bin/can_setup.sh
python3 scripts/mevita_main.py
```

## 🧪 测试Xbox手柄功能

### 测试手柄连接
```bash
# 检查手柄设备
ls /dev/input/js*

# 测试手柄输入
jstest /dev/input/js0
```

### 测试ROS话题
```bash
# 启动手柄节点
rosrun joy joy_node _dev:=/dev/input/js0 _topic:=/xboxjoy/joy

# 监听手柄数据
rostopic echo /xboxjoy/joy
```

### 使用专用测试脚本
```bash
# 启动Xbox手柄测试器
python3 scripts/test_xbox_controller.py
```

## 🔧 故障排除

### 手柄无法识别
1. 检查USB连接或蓝牙配对
2. 确认设备文件存在：`ls /dev/input/js*`
3. 检查权限：`sudo chmod 666 /dev/input/js0`

### ROS话题无数据
1. 确认joy节点正在运行：`rosnode list | grep joy`
2. 检查话题：`rostopic list | grep joy`
3. 重启joy节点

### 控制无响应
1. 确认机器人处于正确状态（STANDBY/STANDUP/WALK）
2. 检查手柄按键映射是否正确
3. 查看控制台输出的调试信息

## 📋 控制流程

1. **初始状态**: 机器人处于STANDBY（待机）状态
2. **按A按键**: 切换到STANDUP（站立）状态
3. **按B按键**: 切换到WALK（行走）状态
4. **使用摇杆**: 在WALK状态下控制机器人移动

## 🔄 向后兼容性

本实现保持了与PS4手柄的完全兼容性：
- 可以同时连接Xbox和PS4手柄
- 优先级：PS4手柄 > Xbox手柄 > SpaceNav > 虚拟手柄
- 原有的PS4手柄功能完全保留

## 📝 技术细节

### 数据结构
- Xbox手柄轴数据：8个轴（通常）
- Xbox手柄按键数据：11个按键（通常）
- 数据格式：ROS sensor_msgs/Joy消息

### 控制算法
- 死区处理：防止摇杆漂移
- 命令限制：输出范围限制在[-1.0, 1.0]
- 实时控制：20Hz控制频率

## 🆘 支持

如遇问题，请：
1. 查看本指南的故障排除部分
2. 检查ROS日志：`roslog`
3. 使用测试脚本验证手柄功能
4. 查看原始PS4手柄实现作为参考
