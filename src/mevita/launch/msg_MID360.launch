<launch>

  <!--user configure parameters for ros start-->
  <arg name="lvx_file_path" default="livox_test.lvx"/>
  <arg name="bd_list" default="100000000000000"/>
  <arg name="xfer_format" default="1"/> <!-- 0: point cloud, 1: custom point cloud, 2: standard ros point cloud -->
  <arg name="multi_topic" default="0"/>
  <arg name="data_src" default="0"/>
  <arg name="publish_freq" default="10.0"/>
  <arg name="output_type" default="0"/>
  <arg name="cmdline_arg" default="$(arg bd_list)"/>
  <arg name="msg_frame_id" default="livox_frame"/>
  <arg name="user_config_path" default="MID360_config.json"/>
  <arg name="lidar_bag" default="true"/>
  <arg name="imu_bag" default="true"/>
  <!--user configure parameters for ros end--> 

  <param name="xfer_format" value="$(arg xfer_format)"/>
  <param name="multi_topic" value="$(arg multi_topic)"/>
  <param name="data_src" value="$(arg data_src)"/>
  <param name="publish_freq" type="double" value="$(arg publish_freq)"/>
  <param name="output_data_type" value="$(arg output_type)"/>
  <param name="cmdline_str" type="string" value="$(arg bd_list)"/>
  <param name="cmdline_file_path" type="string" value="$(arg lvx_file_path)"/>
  <param name="user_config_path" type="string" value="$(find mevita)/config/$(arg user_config_path)"/>
  <param name="frame_id" type="string" value="$(arg msg_frame_id)"/>
  <param name="enable_lidar_bag" type="bool" value="$(arg lidar_bag)"/>
  <param name="enable_imu_bag" type="bool" value="$(arg imu_bag)"/>

  <node name="livox_lidar_publisher2" pkg="livox_ros_driver2"
    type="livox_ros_driver2_node" required="true"
    output="screen" args="$(arg cmdline_arg)"/>

</launch>
