<launch>
  <arg name="model" default="mevita_dae" />
  <arg name="gui" default="false" />

  <param name="robot_description" textfile="$(find mevita)/models/$(arg model).urdf" />
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" />
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui" if="$(arg gui)" />
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find mevita)/config/mevita.rviz" />
</launch>
