Traceback (most recent call last):
  File "/opt/ros/noetic/share/genpy/cmake/../../../lib/genpy/genmsg_py.py", line 43, in <module>
    import genpy.generator
  File "/opt/ros/noetic/lib/python3/dist-packages/genpy/__init__.py", line 34, in <module>
    from . message import Message, SerializationError, DeserializationError, MessageException, struct_I
  File "/opt/ros/noetic/lib/python3/dist-packages/genpy/message.py", line 48, in <module>
    import yaml
ModuleNotFoundError: No module named 'yaml'
make[2]: *** [CMakeFiles/mevita_generate_messages_py.dir/build.make:76: /home/<USER>/mevita_ws/devel/.private/mevita/lib/python3/dist-packages/mevita/msg/_MevitaLog.py] Error 1
make[1]: *** [CMakeFiles/Makefile2:710: CMakeFiles/mevita_generate_messages_py.dir/all] Error 2
make[1]: *** Waiting for unfinished jobs....
Traceback (most recent call last):
  File "/opt/ros/noetic/share/gencpp/cmake/../../../lib/gencpp/gen_cpp.py", line 49, in <module>
    genmsg.template_tools.generate_from_command_line_options(
  File "/opt/ros/noetic/lib/python3/dist-packages/genmsg/template_tools.py", line 213, in generate_from_command_line_options
    generate_from_file(argv[1], options.package, options.outdir, options.emdir, options.includepath, msg_template_dict, srv_template_dict)
  File "/opt/ros/noetic/lib/python3/dist-packages/genmsg/template_tools.py", line 154, in generate_from_file
    _generate_msg_from_file(input_file, output_dir, template_dir, search_path, package_name, msg_template_dict)
  File "/opt/ros/noetic/lib/python3/dist-packages/genmsg/template_tools.py", line 93, in _generate_msg_from_file
    _generate_from_spec(input_file,
  File "/opt/ros/noetic/lib/python3/dist-packages/genmsg/template_tools.py", line 77, in _generate_from_spec
    interpreter = em.Interpreter(output=ofile, globals=g, options={em.RAW_OPT:True,em.BUFFERED_OPT:True})
                                                                   ^^^^^^^^^^
AttributeError: module 'em' has no attribute 'RAW_OPT'
make[2]: *** [CMakeFiles/mevita_generate_messages_cpp.dir/build.make:76: /home/<USER>/mevita_ws/devel/.private/mevita/include/mevita/MevitaLog.h] Error 1
make[2]: *** Deleting file '/home/<USER>/mevita_ws/devel/.private/mevita/include/mevita/MevitaLog.h'
make[1]: *** [CMakeFiles/Makefile2:498: CMakeFiles/mevita_generate_messages_cpp.dir/all] Error 2
make: *** [Makefile:146: all] Error 2
