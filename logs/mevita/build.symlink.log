Symlinking /home/<USER>/mevita_ws/devel/share/gennodejs/ros/mevita/_index.js
Symlinking /home/<USER>/mevita_ws/devel/share/gennodejs/ros/mevita/msg/_index.js
Symlinking /home/<USER>/mevita_ws/devel/share/gennodejs/ros/mevita/msg/MevitaLog.js
Symlinking /home/<USER>/mevita_ws/devel/share/common-lisp/ros/mevita/msg/_package.lisp
Symlinking /home/<USER>/mevita_ws/devel/share/common-lisp/ros/mevita/msg/MevitaLog.lisp
Symlinking /home/<USER>/mevita_ws/devel/share/common-lisp/ros/mevita/msg/mevita-msg.asd
Symlinking /home/<USER>/mevita_ws/devel/share/common-lisp/ros/mevita/msg/_package_MevitaLog.lisp
Symlinking /home/<USER>/mevita_ws/devel/share/roseus/ros/mevita/manifest.l
Symlinking /home/<USER>/mevita_ws/devel/share/roseus/ros/mevita/msg/MevitaLog.l
Symlinking /home/<USER>/mevita_ws/devel/share/mevita/cmake/mevita-msg-paths.cmake
Symlinking /home/<USER>/mevita_ws/devel/share/mevita/cmake/mevitaConfig.cmake
Symlinking /home/<USER>/mevita_ws/devel/share/mevita/cmake/mevita-msg-extras.cmake
Symlinking /home/<USER>/mevita_ws/devel/share/mevita/cmake/mevitaConfig-version.cmake
Symlinking /home/<USER>/mevita_ws/devel/lib/pkgconfig/mevita.pc
Symlinking /home/<USER>/mevita_ws/devel/lib/python3/dist-packages/mevita/__init__.py
Symlinking /home/<USER>/mevita_ws/devel/lib/python3/dist-packages/mevita/msg/_MevitaLog.py
Symlinking /home/<USER>/mevita_ws/devel/lib/python3/dist-packages/mevita/msg/__init__.py
Symlinking /home/<USER>/mevita_ws/devel/include/mevita/MevitaLog.h
