Symlinking /home/<USER>/mevita_ws/devel/./local_setup.bash
Symlinking /home/<USER>/mevita_ws/devel/./local_setup.zsh
Symlinking /home/<USER>/mevita_ws/devel/./setup.fish
Symlinking /home/<USER>/mevita_ws/devel/./local_setup.sh
Symlinking /home/<USER>/mevita_ws/devel/./setup.sh
Symlinking /home/<USER>/mevita_ws/devel/./_setup_util.py
Symlinking /home/<USER>/mevita_ws/devel/./env.sh
Symlinking /home/<USER>/mevita_ws/devel/./local_setup.fish
Symlinking /home/<USER>/mevita_ws/devel/./setup.bash
Symlinking /home/<USER>/mevita_ws/devel/./setup.zsh
Symlinking /home/<USER>/mevita_ws/devel/share/catkin_tools_prebuild/cmake/catkin_tools_prebuildConfig-version.cmake
Symlinking /home/<USER>/mevita_ws/devel/share/catkin_tools_prebuild/cmake/catkin_tools_prebuildConfig.cmake
Symlinking /home/<USER>/mevita_ws/devel/lib/pkgconfig/catkin_tools_prebuild.pc
