Not searching for unused variables given on the command line.
[36m--[0m Using CATKIN_DEVEL_PREFIX: /home/<USER>/mevita_ws/devel/.private/catkin_tools_prebuild
[36m--[0m Using CMAKE_PREFIX_PATH: /opt/ros/noetic
[36m--[0m This workspace overlays: /opt/ros/noetic
[33mCMake Warning (dev) at /opt/ros/noetic/share/catkin/cmake/python.cmake:4 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

[36mCall Stack (most recent call first):[0m
  /opt/ros/noetic/share/catkin/cmake/all.cmake:164 (include)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:4 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[36m--[0m Using PYTHON_EXECUTABLE: /home/<USER>/miniconda3/bin/python3
[36m--[0m Using Debian Python package layout
[36m--[0m Could NOT find PY_em (missing: PY_EM)
[31mCMake Error at /opt/ros/noetic/share/catkin/cmake/empy.cmake:30 (message):
  Unable to find either executable 'empy' or Python module 'em'...  try
  installing the package 'python3-empy'
[36mCall Stack (most recent call first):[0m
  /opt/ros/noetic/share/catkin/cmake/all.cmake:164 (include)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:4 (find_package)

[0m
[36m--[0m Configuring incomplete, errors occurred!
