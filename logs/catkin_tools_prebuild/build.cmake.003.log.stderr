[33mCMake Warning (dev) at /opt/ros/noetic/share/catkin/cmake/python.cmake:4 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

[36mCall Stack (most recent call first):[0m
  /opt/ros/noetic/share/catkin/cmake/all.cmake:164 (include)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:4 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[0mCMake Deprecation Warning at /usr/src/googletest/CMakeLists.txt:4 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[0mCMake Deprecation Warning at /usr/src/googletest/googlemock/CMakeLists.txt:45 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[0mCMake Deprecation Warning at /usr/src/googletest/googletest/CMakeLists.txt:56 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[33mCMake Warning (dev) at /usr/src/googletest/googletest/cmake/internal_utils.cmake:249 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

[36mCall Stack (most recent call first):[0m
  /usr/src/googletest/googletest/CMakeLists.txt:91 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
ImportError: "from catkin_pkg.package import parse_package" failed: No module named 'catkin_pkg'
Make sure that you have installed "catkin_pkg", it is up to date and on the PYTHONPATH.
[31mCMake Error at /opt/ros/noetic/share/catkin/cmake/safe_execute_process.cmake:11 (message):
  execute_process(/home/<USER>/miniconda3/bin/python3
  "/opt/ros/noetic/share/catkin/cmake/parse_package_xml.py"
  "/opt/ros/noetic/share/catkin/cmake/../package.xml"
  "/home/<USER>/mevita_ws/build/catkin_tools_prebuild/catkin/catkin_generated/version/package.cmake")
  returned error code 1
[36mCall Stack (most recent call first):[0m
  /opt/ros/noetic/share/catkin/cmake/catkin_package_xml.cmake:74 (safe_execute_process)
  /opt/ros/noetic/share/catkin/cmake/all.cmake:168 (_catkin_package_xml)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:4 (find_package)

[0m
